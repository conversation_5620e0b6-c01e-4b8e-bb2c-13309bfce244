// Mobile Menu Toggle
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
    const mobileNav = document.querySelector('.mobile-nav');
    let isMenuOpen = false;

    if (mobileMenuToggle && mobileNav) {
        mobileMenuToggle.addEventListener('click', function() {
            isMenuOpen = !isMenuOpen;
            
            if (isMenuOpen) {
                mobileNav.style.display = 'block';
                // Animate menu toggle
                const spans = mobileMenuToggle.querySelectorAll('span');
                spans[0].style.transform = 'rotate(45deg) translate(5px, 5px)';
                spans[1].style.opacity = '0';
                spans[2].style.transform = 'rotate(-45deg) translate(7px, -6px)';
            } else {
                mobileNav.style.display = 'none';
                // Reset menu toggle
                const spans = mobileMenuToggle.querySelectorAll('span');
                spans[0].style.transform = 'none';
                spans[1].style.opacity = '1';
                spans[2].style.transform = 'none';
            }
        });
    }

    // Close mobile menu when clicking outside
    document.addEventListener('click', function(event) {
        if (isMenuOpen && !mobileMenuToggle.contains(event.target) && !mobileNav.contains(event.target)) {
            isMenuOpen = false;
            mobileNav.style.display = 'none';
            // Reset menu toggle
            const spans = mobileMenuToggle.querySelectorAll('span');
            spans[0].style.transform = 'none';
            spans[1].style.opacity = '1';
            spans[2].style.transform = 'none';
        }
    });

    // Close mobile menu on window resize
    window.addEventListener('resize', function() {
        if (window.innerWidth > 768 && isMenuOpen) {
            isMenuOpen = false;
            mobileNav.style.display = 'none';
            // Reset menu toggle
            const spans = mobileMenuToggle.querySelectorAll('span');
            spans[0].style.transform = 'none';
            spans[1].style.opacity = '1';
            spans[2].style.transform = 'none';
        }
    });
});

// Smooth Scrolling for Anchor Links
document.addEventListener('DOMContentLoaded', function() {
    const links = document.querySelectorAll('a[href^="#"]');
    
    links.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);
            
            if (targetSection) {
                const headerHeight = document.querySelector('.header').offsetHeight;
                const targetPosition = targetSection.offsetTop - headerHeight;
                
                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
            }
        });
    });
});

// Header Scroll Effect
document.addEventListener('DOMContentLoaded', function() {
    const header = document.querySelector('.header');
    let lastScrollTop = 0;

    window.addEventListener('scroll', function() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        
        if (scrollTop > 100) {
            header.style.background = 'rgba(255, 255, 255, 0.95)';
            header.style.backdropFilter = 'blur(10px)';
        } else {
            header.style.background = '#ffffff';
            header.style.backdropFilter = 'none';
        }

        lastScrollTop = scrollTop;
    });

    // Banner Slider Functionality
    const slides = document.querySelectorAll('.banner-slide');
    const dots = document.querySelectorAll('.dot');
    const arrowLeft = document.querySelector('.arrow-left');
    const arrowRight = document.querySelector('.arrow-right');
    let currentSlide = 0;
    let slideInterval;

    function showSlide(index) {
        // Remove active class from all slides and dots
        slides.forEach(slide => slide.classList.remove('active'));
        dots.forEach(dot => dot.classList.remove('active'));

        // Add active class to current slide and dot
        slides[index].classList.add('active');
        dots[index].classList.add('active');

        currentSlide = index;
    }

    function nextSlide() {
        const next = (currentSlide + 1) % slides.length;
        showSlide(next);
    }

    function prevSlide() {
        const prev = (currentSlide - 1 + slides.length) % slides.length;
        showSlide(prev);
    }

    function startSlideshow() {
        slideInterval = setInterval(nextSlide, 4000); // Change slide every 4 seconds
    }

    function stopSlideshow() {
        clearInterval(slideInterval);
    }

    // Event listeners for navigation
    if (arrowRight) {
        arrowRight.addEventListener('click', () => {
            nextSlide();
            stopSlideshow();
            startSlideshow(); // Restart the timer
        });
    }

    if (arrowLeft) {
        arrowLeft.addEventListener('click', () => {
            prevSlide();
            stopSlideshow();
            startSlideshow(); // Restart the timer
        });
    }

    // Event listeners for dots
    dots.forEach((dot, index) => {
        dot.addEventListener('click', () => {
            showSlide(index);
            stopSlideshow();
            startSlideshow(); // Restart the timer
        });
    });

    // Pause slideshow on hover
    const bannerSlider = document.querySelector('.banner-slider');
    if (bannerSlider) {
        bannerSlider.addEventListener('mouseenter', stopSlideshow);
        bannerSlider.addEventListener('mouseleave', startSlideshow);
    }

    // Enhanced Touch/Swipe functionality for mobile
    let touchStartX = 0;
    let touchEndX = 0;
    let touchStartY = 0;
    let touchEndY = 0;
    let isScrolling = false;

    function handleSwipe() {
        const swipeThreshold = 50; // Minimum distance for a swipe
        const swipeDistance = touchEndX - touchStartX;
        const verticalDistance = Math.abs(touchEndY - touchStartY);

        // Only handle horizontal swipes (not vertical scrolling)
        if (Math.abs(swipeDistance) > swipeThreshold && verticalDistance < 100) {
            if (swipeDistance > 0) {
                // Swipe right - go to previous slide
                prevSlide();
            } else {
                // Swipe left - go to next slide
                nextSlide();
            }
            stopSlideshow();
            startSlideshow(); // Restart the timer
        }
    }

    if (bannerSlider) {
        bannerSlider.addEventListener('touchstart', (e) => {
            touchStartX = e.changedTouches[0].screenX;
            touchStartY = e.changedTouches[0].screenY;
            isScrolling = false;
        }, { passive: true });

        bannerSlider.addEventListener('touchmove', (e) => {
            if (!touchStartX || !touchStartY) return;

            const touchX = e.changedTouches[0].screenX;
            const touchY = e.changedTouches[0].screenY;
            const diffX = Math.abs(touchStartX - touchX);
            const diffY = Math.abs(touchStartY - touchY);

            // Determine if user is scrolling vertically
            if (diffY > diffX) {
                isScrolling = true;
            }

            // Prevent default only for horizontal swipes
            if (!isScrolling && diffX > 10) {
                e.preventDefault();
            }
        }, { passive: false });

        bannerSlider.addEventListener('touchend', (e) => {
            if (!isScrolling) {
                touchEndX = e.changedTouches[0].screenX;
                touchEndY = e.changedTouches[0].screenY;
                handleSwipe();
            }

            // Reset values
            touchStartX = 0;
            touchStartY = 0;
            touchEndX = 0;
            touchEndY = 0;
            isScrolling = false;
        }, { passive: true });
    }

    // Start the slideshow
    startSlideshow();

    // Floating Play Now Button functionality
    const floatingPlayBtn = document.getElementById('floatingPlayBtn');
    const inviteSection = document.querySelector('.invite-section');

    function toggleFloatingButton() {
        if (!floatingPlayBtn || !inviteSection) return;

        const inviteSectionRect = inviteSection.getBoundingClientRect();
        const windowHeight = window.innerHeight;

        // Hide floating button ONLY when the invite section is visible in viewport
        // Show it in ALL other cases (including banner, about, slots, etc.)
        if (inviteSectionRect.top < windowHeight && inviteSectionRect.bottom > 0) {
            // Invite section is visible in viewport - hide floating button
            floatingPlayBtn.classList.remove('show');
        } else {
            // Invite section is not visible - show floating button
            floatingPlayBtn.classList.add('show');
        }
    }

    // Check on scroll
    window.addEventListener('scroll', toggleFloatingButton);

    // Check on page load
    toggleFloatingButton();
});

// Image Loading Animation
document.addEventListener('DOMContentLoaded', function() {
    const images = document.querySelectorAll('img');
    
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.style.opacity = '0';
                img.style.transform = 'translateY(20px)';
                img.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                
                img.onload = function() {
                    img.style.opacity = '1';
                    img.style.transform = 'translateY(0)';
                };
                
                // If image is already loaded
                if (img.complete) {
                    img.style.opacity = '1';
                    img.style.transform = 'translateY(0)';
                }
                
                observer.unobserve(img);
            }
        });
    });
    
    images.forEach(img => {
        imageObserver.observe(img);
    });
});

// Section Animation on Scroll
document.addEventListener('DOMContentLoaded', function() {
    const sections = document.querySelectorAll('section');
    
    const sectionObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });
    
    sections.forEach(section => {
        section.style.opacity = '0';
        section.style.transform = 'translateY(30px)';
        section.style.transition = 'opacity 0.8s ease, transform 0.8s ease';
        sectionObserver.observe(section);
    });
});

// Button Hover Effects
document.addEventListener('DOMContentLoaded', function() {
    const buttons = document.querySelectorAll('.btn, .invite-btn');
    
    buttons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });
        
        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
});

// Gallery Auto-pause on Hover
document.addEventListener('DOMContentLoaded', function() {
    const galleryTrack = document.querySelector('.gallery-track');
    
    if (galleryTrack) {
        galleryTrack.addEventListener('mouseenter', function() {
            this.style.animationPlayState = 'paused';
        });
        
        galleryTrack.addEventListener('mouseleave', function() {
            this.style.animationPlayState = 'running';
        });
    }
});

// FAQ Accordion Effect (if needed)
document.addEventListener('DOMContentLoaded', function() {
    const faqItems = document.querySelectorAll('.faq-item');
    faqItems.forEach(item => {
        const question = item.querySelector('h3');
        const answer = item.querySelector('p');
        if (question && answer) {
            question.setAttribute('tabindex', '0');
            question.setAttribute('role', 'button');
            question.setAttribute('aria-expanded', 'true');
            answer.style.maxHeight = 'none';
            question.addEventListener('click', function() {
                const expanded = question.getAttribute('aria-expanded') === 'true';
                question.setAttribute('aria-expanded', !expanded);
                if (expanded) {
                    answer.style.maxHeight = '0';
                    answer.style.overflow = 'hidden';
                    answer.style.transition = 'max-height 0.4s ease';
                } else {
                    answer.style.maxHeight = '200px';
                    answer.style.overflow = 'visible';
                    answer.style.transition = 'max-height 0.4s ease';
                }
            });
            question.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    question.click();
                }
            });
        }
    });
});

// Loading Screen (Optional)
document.addEventListener('DOMContentLoaded', function() {
    // Add a simple loading effect
    document.body.style.opacity = '0';
    document.body.style.transition = 'opacity 0.5s ease';
    
    window.addEventListener('load', function() {
        document.body.style.opacity = '1';
    });
});

// Parallax Effect for Hero Section
document.addEventListener('DOMContentLoaded', function() {
    const hero = document.querySelector('.hero');
    
    if (hero) {
        window.addEventListener('scroll', function() {
            const scrolled = window.pageYOffset;
            const rate = scrolled * -0.5;
            hero.style.transform = `translateY(${rate}px)`;
        });
    }
});

// Scroll to Top Button
const scrollToTopBtn = document.getElementById('scrollToTopBtn');
window.addEventListener('scroll', function() {
    if (window.scrollY > 300) {
        scrollToTopBtn.style.display = 'block';
    } else {
        scrollToTopBtn.style.display = 'none';
    }
});
scrollToTopBtn.addEventListener('click', function() {
    window.scrollTo({ top: 0, behavior: 'smooth' });
});

// Mobile-specific optimizations
document.addEventListener('DOMContentLoaded', function() {
    // Detect mobile device
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

    if (isMobile) {
        // Add mobile class to body
        document.body.classList.add('mobile-device');

        // Optimize scroll performance
        let ticking = false;
        function updateScrollPosition() {
            // Your scroll-based functions here
            ticking = false;
        }

        function requestTick() {
            if (!ticking) {
                requestAnimationFrame(updateScrollPosition);
                ticking = true;
            }
        }

        // Throttle scroll events
        window.addEventListener('scroll', requestTick, { passive: true });

        // Improve touch responsiveness
        document.addEventListener('touchstart', function() {}, { passive: true });

        // Prevent zoom on double tap for specific elements
        const preventZoomElements = document.querySelectorAll('.btn, .invite-btn, .floating-play-btn, .arrow-left, .arrow-right, .dot');
        preventZoomElements.forEach(element => {
            element.addEventListener('touchend', function(e) {
                e.preventDefault();
                element.click();
            });
        });

        // Optimize image loading for mobile
        const images = document.querySelectorAll('img');
        images.forEach(img => {
            img.loading = 'lazy';
            img.decoding = 'async';
        });
    }

    // Viewport height fix for mobile browsers
    function setVH() {
        const vh = window.innerHeight * 0.01;
        document.documentElement.style.setProperty('--vh', `${vh}px`);
    }

    setVH();
    window.addEventListener('resize', setVH);
    window.addEventListener('orientationchange', setVH);
});

// Console Welcome Message
console.log('%c🎰 Welcome to Buenas Casino! 🎰', 'color: #ff6b35; font-size: 20px; font-weight: bold;');
console.log('%cEnjoy the thrill of the game!', 'color: #f7931e; font-size: 14px;');
